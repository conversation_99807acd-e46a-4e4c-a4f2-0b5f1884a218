/**
 * This modal dialog is intended to be used in conjunction (i.e. from) an
 * Assessment OR standalone (in which case, the user will have to upload a
 * scratch file).
 *
 * Since most of the value here are overridable but we don't want to modify the
 * original Assessment/Option/Building, we instead use the passed in values to
 * populate our initial selected values, and afterwards ONLY modify the
 * 'selected' variables.
 *
 * No plans for bulk-upload functionality YET but you know it's coming...
 */
(function () {

    'use strict';

    let controllerId = 'exportGlazingCalcModalController';

    angular.module('app')
        .controller(controllerId, ['common', '$rootScope', '$scope', '$mdDialog', '$stateParams', '$state', '$q',
            'bootstrap.dialog', 'uuid4', 'zoneservice', 'zonesummaryservice', 'glazingcalculatorexportservice',
            'adjacentfloorcoveringservice', 'coreLoop', 'assessmentsoftwareservice',
            'compliancemethodservice', 'assessmentcomplianceoptionservice', 'fileservice',
            'certification2019service', 'certification2022service',
            exportGlazingCalcModalController]);

    function exportGlazingCalcModalController(common, $rootScope, $scope, $mdDialog, $stateParams, $state, $q,
                                              modalDialog, uuid4, zoneservice, zonesummaryservice, glazingcalculatorexportservice,
                                              adjacentfloorcoveringservice, coreLoop, assessmentsoftwareservice,
                                              compliancemethodservice, assessmentcomplianceoptionservice, fileservice,
                                              certification2019service, certification2022service) {

        // The model for this form
        const vm = this;

        // As-is assessment data (from assessment, if applicable)
        vm.source = $scope.source;
        vm.mode = $scope.mode || "advanced";
        vm.assessment = angular.copy($scope.assessment);
        vm.option = angular.copy($scope.option);
        vm.building = angular.copy($scope.building);
        vm.allComplianceOptions = angular.copy(vm.assessment.allComplianceOptions);

        // Data (External or inferred)
        vm.simulationOptions = [];
        vm.assessmentSoftwareList = $scope.assessmentSoftwareList;
        vm.certificationList = $scope.certificationList;
        vm.nccClimateZoneList = $scope.nccClimateZoneList;
        vm.sectorDeterminationList = $scope.sectorDeterminationList;
        vm.availableWallInsulationOptions = [];
        vm.adjacentFloorCoveringList = [];
        vm.selectedSectorDetermination = {}

        vm.scratchData = {};

        vm.multiOrientation = false;

        // All possible opening description formats.
        vm.glazingFormats = [
            { id: "selectAll", order: -10, title: "Select All" },
            { id: "opening",   order:   0, title: "Opening Number" },
            { id: "storey",    order:   5, title: "Storey" },
            { id: "zone",      order:  10, title: "Parent Zone" },
            { id: "style",     order:  20, title: "Opening Style" },
            { id: "frame",     order:  30, title: "Frame" },
            { id: "glass",     order:  40, title: "Glass Description" },
        ];

        // 'selected' variables, storing default or overridden data.
        vm.selectedCertification = null;
        vm.selectedAssessmentSoftware = null;
        vm.selectedNccClimateZone = null;
        vm.selectedNumberOfStoreys = null;
        vm.selectedStoreys = null;

        // Manual overrides that typically do NOT get reset if the simulation option/etc changes (one exception possibly
        // being if the # of storeys changes and then storeys which no longer exist would be blown away.
        vm.manualOverrides = {};

        function initialize() {

            vm.manualOverrides = [];

            if (vm.source ===  "assessment") {

                vm.simulationOptions = determineSimulationOptions(vm.allComplianceOptions);
                vm.selectedCertification = vm.option.certification;
                vm.selectedGlazingFormat = vm.glazingFormats.map(x => x);
                vm.selectedNccClimateZone = vm.assessment.nccClimateZone;
                vm.selectedSectorDetermination = vm.sectorDeterminationList.find(x => x.sectorDeterminationCode == "NCC");

                vm.recalculateSimulationData({option: vm.option, building: vm.building});

            } else if (vm.source === "none") {

                // We're coming from blank and will require a glazing calc to be uploaded.
                vm.selectedGlazingFormat = vm.glazingFormats.map(x => x);
                vm.building = null;
                vm.availableAssessmentSoftwareList = vm.assessmentSoftwareList;

            }

            adjacentfloorcoveringservice.getAll().then(data => vm.adjacentFloorCoveringList = data);

        }

        vm.title = () => "Glazing Calculator Export";

        vm.certification2019 = (certification = null) => certification == null
            ? vm.selectedCertification?.glazingCalculatorRulesetCode === "NCC 2019"
            : certification.glazingCalculatorRulesetCode === "NCC 2019";

        vm.certification2022 = (certification = null) => certification == null
            ? vm.selectedCertification?.glazingCalculatorRulesetCode === "NCC 2022"
            : certification.glazingCalculatorRulesetCode === "NCC 2022";

        vm.cancel = function () {
            $mdDialog.cancel();
        }

        vm.selectScratchFile = function($file, prop) {
            vm.scratchData[prop] = {
                file: $file,
                name: $file.name
            };
        }

        vm.scratchFilesAreValid = function(data, fileTypes) {

            if (vm.selectedAssessmentSoftware == null)
                return false;

            if (vm.selectedCertification == null)
                return false;

            if (vm.selectedNccClimateZone == null)
                return false;

            // Note: Start on 1 to skip 'fileA' (assessment file)
            for(let i = 1; i < fileTypes.length; i++) {
                const fileType = fileTypes[i];
                if (vm.selectedAssessmentSoftware[fileType.prop + 'Required'] === true &&
                   data[fileType.prop] == null)
                    return false;
            }

            return true;
        }

        vm.processScratchData = async function() {

            try {

                vm.processingScratch = true;

                // Essentially what this needs to do is return all data that would
                // usually be returned from from the upload process, and we can use
                // it to construct a 'dummy' building/etc.

                // Convert files to raw text and upload like that (Couldn't figure
                // any other way that worked...)
                const files = [];

                // These must be added in the correct order.
                const b = await vm.scratchData.fileB.file.text();
                files.push(b);

                if (vm.scratchData.fileC?.file != null) {
                    const c = await vm.scratchData.fileC.file.text();
                    files.push(c);
                }

                // console.log("Files: ", files);

                const data = await assessmentcomplianceoptionservice.processScratchFiles(
                    vm.selectedAssessmentSoftware.assessmentSoftwareCode,
                    files);

                vm.building = {};
                await assessmentcomplianceoptionservice.applyScratchData(vm.building, data);

                // Run our core loop functions twice (this is in case some calcs at the end of the first loop affect
                // the start of the second loop and so on). More than twice shouldn't be needed...
                coreLoop.runCoreLoop(vm.building, Number(vm.selectedNccClimateZone.description));
                coreLoop.runCoreLoop(vm.building, Number(vm.selectedNccClimateZone.description));

                // Only take storeys that have glazing when determining this value.
                const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.building.openings);
                vm.selectedNumberOfStoreys = storeysWithGlazing.length;
                vm.selectedStoreys = vm.building.storeys.filter(x => storeysWithGlazing.some(y => y === x.floor));

                vm.buildingDetails = calculateBuildingDetails(vm.selectedCertification, vm.building);

                vm.processingScratch = false;

            } catch(e) {

                vm.processingScratch = false;
                throw e;
            }
        }

        vm.airMovementIsValid = function(storey) {
            return (!(storey.airMovement === "Fail" ||
                storey.airMovement.startsWith("Invalid") ||
                storey.airMovement.startsWith("Insufficient")));
        }

        vm.dataIsValid = function() {

            // TODO: Ensure all entered data is correct. Kinda assuming this stuff atm.

            if ($scope?.glazingExportForm == null || $scope?.glazingExportForm?.$invalid)
                return false;

            if (vm.buildingDetails == null)
                return false;

            if (vm.certification2019()) {
                let airMovementOk = true;

                vm.buildingDetails?.storeys?.forEach(storey => {
                    if (storey.airMovement === "Fail" || storey.airMovement.startsWith("Invalid"))
                        airMovementOk = false;
                });

                if (!airMovementOk)
                    return false;

            }

            if (vm.selectedSectorDetermination.sectorDeterminationCode == 'NatHERS')
                return false;

            if (vm.source === 'none') {
                // TODO: Confirm scratch file has been uploaded...
            }

            return true;
        }

        vm.export = async function () {

            try {

                vm.processing = true;
                const uploadData = prepareUploadData();

                if (vm.certification2019() && !vm.multiOrientation) {
                    await glazingcalculatorexportservice.generateGlazingCalculator2019(uploadData, determineExportFilename(vm.buildingDescription, true, true));
                } else if (vm.certification2019() && vm.multiOrientation) {
                    await glazingcalculatorexportservice.generateGlazingCalculator2019Multi(uploadData, determineExportFilename(vm.buildingDescription, true, true));
                } else if (vm.certification2022() && !vm.multiOrientation) {
                    await glazingcalculatorexportservice.generateGlazingCalculator2022(uploadData, determineExportFilename(vm.buildingDescription, true, true));
                } else if (vm.certification2022() && vm.multiOrientation) {
                    let resultBase64 = (await glazingcalculatorexportservice.generateGlazingCalculator2022Multi(uploadData, determineExportFilename(vm.buildingDescription, true, true))).data;
                    fileservice.downloadFile(
                        { url: `data:application/zip;base64,${resultBase64}` },
                        determineExportFilename(vm.buildingDescription, true, true)
                    );
                }

                vm.processing = false;
                // $mdDialog.hide();

            } catch (e) {
                vm.processing = false;
                throw e;
            }
        }

        function prepareUploadData() {

            // Pre filter all floor constructions.
            const floorConstructions = vm.building.surfaces
                .map(x => x.elements)
                .reduce((a, b) => [...a, ...b])
                .filter(x => x.category.constructionCategoryCode.contains("Floor") && !x.category.constructionCategoryCode.contains("Subfloor"));

            if (vm.certification2019()) {

                let storeys = {};

                // console.log("Selected storeys: ", vm.selectedStoreys);
                // console.log("Building details storeys: ", vm.buildingDetails.storeys);

                vm.buildingDetails.storeys.forEach(storey => {

                    const windows = [];

                    // Grab all windows on storey, sorted by Azimuth.
                    const windowsInStorey = vm.building.openings
                        .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing" ||
                            x.category.constructionCategoryCode === "InteriorGlazing")
                        .map(x => x.elements)
                        .reduce((a,b) => [...a, ...b], [])
                        .filter(x => x.storey === storey.floor)
                        .sort((a, b) => a.azimuth - b.azimuth);

                    let windowCount = 0;

                    windowsInStorey.forEach(window => {

                        windowCount++;

                        const windowZones = vm.building.zones.filter(x => x.linkId === window.parentZoneId);

                        if (windowZones == null || windowZones.length === 0)
                            return;

                        const windowZone = windowZones[0];

                        const area = window.width == null || window.height == null
                            ? window.grossArea
                            : window.width * window.height

                        // Determine description based glazing description format.
                        let description = "";

                        if (vm.selectedGlazingFormat.some(x => x.id === "opening"))
                            description += "W" + common.addLeadingZero(windowCount, 3) + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "storey"))
                            description += storey.name + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "zone"))
                            description += windowZone.zoneDescription + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "style"))
                            description += ((window.overrideOpeningStyle?.title || window.openingStyle?.title) || "Unspecified") + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "frame"))
                            description += (window.frameMaterial?.title || "Unspecified") + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "glass"))
                            description += ((window.glassData?.overrideDescription || window.glassData?.description) || "Unspecified") + "_";

                        description = description.slice(0, -1);

                        const h = window.horizontalProjection1?.eave?.verticalOffset != null && window.height != null
                            ? window.horizontalProjection1?.eave?.verticalOffset + window.height
                            : null;

                        const row = {

                            description: description,
                            facingSector: facingSector(window.azimuth),
                            height: window.height || null,
                            width: window.width || null,
                            area: area,

                            uValue: window.performance?.overrideUValue || window.performance?.uValue,
                            shgc: window.performance?.overrideSHGC || window.performance?.shgc,

                            p: window.horizontalProjection1?.eave?.projection || null,
                            h: h
                        };

                        // With the given info, see if any overrides apply
                        applyOverridesIfNeeded(row, window, windowZone);

                        windows.push(row)
                    });

                    // Strip everything but numerical data from storey if required.
                    let airMovement = storey.airMovement;

                    if (!isNaN(airMovement)) {
                        if (Number(airMovement) == 1)
                            airMovement = "Standard";
                        else if (Number(airMovement) >= 2)
                            airMovement = "High"
                        else
                            airMovement = Number(airMovement).toFixed(2)
                    }

                    // Add compiled data to storey kvp
                    storeys[storey.name]  = {

                        storey: storey.name,
                        directContact: Number(storey.direct.toFixed(0)),
                        suspended: Number(storey.suspended.toFixed(0)),
                        airMovement: airMovement,
                        climateZone: Number(vm.selectedNccClimateZone.description),
                        wallInsulationOption: storey.wallInsulation,
                        buildingNameAndDescription: determineExportFilename(vm.buildingDescription, false, false),

                        windows: windows
                    };

                })

                return storeys;

            } else if (vm.certification2022()) {

                const numberOfStoreysDesc = vm.selectedNumberOfStoreys === 1
                    ? "One"
                    : "Two or more";

                const fullBrickConstruction = vm.building.masonryWalls === true
                    ? "Yes, full brick house"
                    : "No";

                const storeys = [];

                let windowCount = 0; // Purely used for window description.

                vm.selectedStoreys.forEach(storey => {

                    const windows = [];

                    // Grab all windows on storey, sorted by Azimuth.
                    const windowsInStorey = vm.building.openings
                        .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing" ||
                                     x.category.constructionCategoryCode === "InteriorGlazing")
                        .map(x => x.elements)
                        .reduce((a,b) => [...a, ...b], [])
                        .filter(x => x.storey === storey.floor)
                        .sort((a, b) => a.azimuth - b.azimuth);

                    windowsInStorey.forEach(window => {

                        windowCount++;

                        const windowZones = vm.building.zones.filter(x => x.linkId === window.parentZoneId);

                        if (windowZones == null || windowZones.length === 0)
                            return;

                        const windowZone = windowZones[0];

                        const adjacentFloorCovering = GlazingCalcConverter
                            .determineAdjacentFloorCovering(window, floorConstructions, vm.adjacentFloorCoveringList);

                        const isBedroomOrUtility = GlazingCalcConverter
                            .determineBedroomOrUtility(window, windowZone, adjacentFloorCovering.adjacentFloorCoveringCode);

                        const levelType = GlazingCalcConverter.determineLevelType(window, floorConstructions);

                        const area = window.width == null || window.height == null
                            ? window.grossArea
                            : window.width * window.height

                        // Determine description based glazing description format.
                        let description = "";

                        if (vm.selectedGlazingFormat.some(x => x.id === "opening"))
                            description += "W" + common.addLeadingZero(windowCount, 3) + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "storey"))
                            description += storey.name + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "zone"))
                            description += windowZone.zoneDescription + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "style"))
                            description += ((window.overrideOpeningStyle?.title || window.openingStyle?.title) || "Unspecified") + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "frame"))
                            description += (window.frameMaterial?.title || "Unspecified") + "_";
                        if (vm.selectedGlazingFormat.some(x => x.id === "glass"))
                            description += ((window.glassData?.overrideDescription || window.glassData?.description) || "Unspecified") + "_";

                        description = description.slice(0, -1);

                        const h = window.horizontalProjection1?.eave?.verticalOffset != null && window.height != null
                            ? window.horizontalProjection1?.eave?.verticalOffset + window.height
                            : null;

                        const row = {
                            description: description,
                            facingSector: facingSector(window.azimuth),
                            height: window.height || null,
                            width: window.width || null,
                            area: area,

                            levelType,

                            frameColour: GlazingCalcConverter.determineColour(window.frameSolarAbsorptance),
                            openability: window.openability + "%",
                            uValue: window.performance?.overrideUValue || window.performance?.uValue,
                            shgc: window.performance?.overrideSHGC || window.performance?.shgc,

                            isBedroomOrUtility,
                            adjacentFloorCovering: adjacentFloorCovering.description,

                            p: window.horizontalProjection1?.eave?.projection || null,
                            h: h,
                        };

                        // With the given info, see if any overrides apply
                        applyOverridesIfNeeded(row, window, windowZone);

                        windows.push(row)
                    });

                    storeys.push({
                    windows,
                    });

                });

                const climateZone = Number(vm.selectedNccClimateZone.description);

                const glazingData = {
                    buildingNameAndDescription: determineExportFilename(vm.buildingDescription, false),
                    numberOfStoreysDesc,
                    directContact: Number(vm.buildingDetails.area.direct.toFixed(0)),
                    suspended: Number(vm.buildingDetails.area.suspended.toFixed(0)),
                    intermediate: Number(vm.buildingDetails.area.overRooms.toFixed(0)),
                    climateZone,
                    fullBrickConstruction: climateZone === 5 // Must be blank unless climate zone is 5.
                        ? fullBrickConstruction
                        : null,
                    storeys,
                };

                console.log("Uploading glazing data: ", glazingData);

                return glazingData;
            } else
                throw 'Unknown certification selected - cannot generate glazing calc!';

        }

        function optionTitle(option) {
            return option.isBaselineSimulation == true
                ? "Baseline"
                : "Option " + option.optionIndex;
        }

        function referenceBuildingTitle(option) {
            return option.complianceMethod.complianceMethodCode === "CMPerfSolution"
                ? "Reference"
                : "Deemed-to-Satisfy"
        }

        function determineExportFilename(buildingDescription, useUnderscores, includeGc) {
            let filename = buildingDescription;
            if (includeGc && vm.source === "assessment") {
                filename = filename.replace(vm.assessment.assessmentProjectDetail.clientJobNumber, vm.assessment.assessmentProjectDetail.clientJobNumber + "_GC");
            }
            if (useUnderscores) {
                filename = filename.replaceAll(" - ", "_");
            }
            return filename;
        }

        /** Populates our simulation selection dropdown */
        function determineSimulationOptions(options) {

            const optionData = [];

            // Construct all possible selectable option/building values.
            for(let i = 0; i < options.length; i++) {
                const option = options[i];

                const optTitle = optionTitle(option);

                optionData.push({
                    option: option,
                    building: option.proposed,
                    title: `${optTitle} - Proposed Building`
                })

                // If this option requires a reference, show it as well.
                if (option.complianceMethod.complianceMethodCode === "CMPerfSolutionDTS" ||
                    option.complianceMethod.complianceMethodCode === "CMPerfSolution") {

                    const buildingTitle = referenceBuildingTitle(option);

                    optionData.push({
                        option: option,
                        building: option.reference,
                        title: `${optTitle} - ${buildingTitle} Building`
                    })
                }
            }

            return optionData;
        }

        vm.updateDataLinkedToCertification = function(certification) {
            if (certification.sectorDetermination != null)
                vm.selectedSectorDetermination = certification.sectorDetermination;
        }

        /** Applies selected building defaults to all 'selected' values */
        vm.recalculateSimulationData = function(selectedSimulation = null, certification = null) {

            if (vm.source === "none")
                return;

            if (selectedSimulation == null)
                selectedSimulation = { option: vm.option, building: vm.building };

            if (certification == null)
                certification = vm.selectedCertification;

            vm.option = angular.copy(selectedSimulation.option);
            vm.building = angular.copy(selectedSimulation.building);

            let clientJobNumber = vm.assessment.assessmentProjectDetail.clientJobNumber;
            let address = vm.assessment.assessmentProjectDetail.useCustomAddress
                ? vm.assessment.assessmentProjectDetail.customDisplayAddress.replace(/\n/g, ', ')
                : vm.assessment.assessmentProjectDetail.fullAddress;
            let optionName = optionTitle(vm.option);
            let buildingTitle = vm.building.assessmentComplianceBuildingId === vm.option.proposed.assessmentComplianceBuildingId
                                    ? "Proposed"
                                    : referenceBuildingTitle(vm.option);

            // Client Job Number - Project Address - Baseline/Option 1/Option 2 - Proposed/Reference/Deemed-to-Satisfy Building
            vm.buildingDescription = `${clientJobNumber} - ${address} - ${optionName} - ${buildingTitle} Building`;

            vm.selectedAssessmentSoftware = vm.option.assessmentSoftware;
            vm.availableAssessmentSoftwareList = determineAvailableSoftware(vm.option.complianceMethod.complianceMethodCode);

            const storeysWithGlazing = zoneservice.determineStoreysWithGlazing(vm.building.openings);
            vm.selectedNumberOfStoreys = storeysWithGlazing.length;
            vm.selectedStoreys = vm.building.storeys.filter(x => storeysWithGlazing.some(y => y === x.floor));

            vm.selectedIsFullMasonry = vm.building.masonryWalls;

            if (vm.certification2019(certification)) {

                vm.availableWallInsulationOptions = certification2019service
                    .determineAvailableInsulationOptions(vm.selectedNccClimateZone);

            } else if (vm.certification2022(certification))
                vm.selectedNumberOfStoreys = storeysWithGlazing.length >= 2 ? 2 : 1;

            vm.buildingDetails = calculateBuildingDetails(certification, vm.building);

            // Since some pretty important data like Zones can change when changing simulation, we blow away all old
            // manual overrides.
            vm.manualOverrides = [];
        }

        /**
         * Blow away any custom data stored in the base assessments storeys
         * and generate new ones. Old storey names retained if possible.
         * @param count The new number of storeys.
         */
        vm.updateStoreys = function(count) {

            let newStoreys = assessmentcomplianceoptionservice.generateStoreys(count, vm.selectedStoreys);

            assessmentcomplianceoptionservice.setStoreyNames(newStoreys);
            vm.selectedStoreys = newStoreys;
        }

        vm.validateAirMovement = function(storey) {

            // If string
            if (isNaN(storey.airMovement)) {
                if (!(storey.airMovement === "N/A" || storey.airMovement === "Std" || storey.airMovement === "Standard" || storey.airMovement === "High"))
                    storey.airMovement = "Invalid input. Acceptable values are 'Standard', 'High', 'N/A' or a value greater than or equal to 1.";
            } else {
                if (storey.airMovement < 1)
                    storey.airMovement = "Invalid input. Acceptable values are 'Standard', 'High', 'N/A' or a value greater than or equal to 1.";
                else
                    storey.airMovement = Number(storey.airMovement).toFixed(2);
            }
        }

        vm.nccClimateZoneChanged = function(climateZone) {
            vm.availableWallInsulationOptions = certification2019service.determineAvailableInsulationOptions(climateZone);
        }

        vm.glazingFormatsOptionClick = (optionSelected) => {
            // function selectAllLogic(allOptions, listBefore, listChildIdKey, selected, selectAllCode)
            vm.selectedGlazingFormat = common.selectAllLogic(vm.glazingFormats, vm.selectedGlazingFormat, "id", optionSelected, "selectAll");
        }

        vm.getGlazingFormatSelectedText = () => {
            if (vm.selectedGlazingFormat.length == 0) {
                return '-';
            } else {
                return vm.selectedGlazingFormat.filter(g => g.id != "selectAll").map(g => g.title).join(" + ");
            }
        }

        function determineAvailableSoftware(complianceMethodCode) {
            return compliancemethodservice
                .determineAvailableSoftware(vm.assessmentSoftwareList, complianceMethodCode);
        }

        /**
         * Runs through the data on the selected building and builds info as required for the given table. Note that a
         * lot of this is specific to this export calc, and thus cannot just be pulled directly from the assessment
         * data, because of course.
         */
        function calculateBuildingDetails(certification, building) {

            const buildingDetails = {};

            if (certification?.glazingCalculatorRulesetCode === "NCC 2019") {

                const storeyDetails = certification2019service.calculateFloorAreas(
                    vm.selectedStoreys,
                    building.zones,
                    building.surfaces);

                buildingDetails.storeys = storeyDetails;

            } else if (certification?.glazingCalculatorRulesetCode === "NCC 2022") {

                const area = certification2022service.calculateFloorAreas(
                    vm.selectedStoreys,
                    building.zones,
                    building.surfaces);
                buildingDetails.area = area;
            }

            return buildingDetails;
        }

        vm.addOverride = function() {
            vm.manualOverrides.push({
                id: uuid4.generate(),
                storey: null,
                group: null,
                selection: null
            });
        }

        vm.deleteOverride = function(override) {
            const index = vm.manualOverrides.indexOf(override);
            vm.manualOverrides.splice(index, 1);
        }

        vm.doNothing = function() {
            // Do nothing...
        }

        function presentFacingSectors(allOpeningElements) {

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};

            const sorted = allOpeningElements
                .sort((a, b) => a.azimuth - b.azimuth);

            sorted.forEach(x => {
                const facing = facingSector(x.azimuth);
                uniques[facing] = { title: facing, code: facing };
            });

            let converted = [{ title: 'All Sectors', code: 'ALL' }];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;
        }

        function presentOpeningStyles(allOpeningElements){

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};

            allOpeningElements.forEach(x => {
                const finalOpeningStyle = x.overrideOpeningStyle || x.openingStyle;
                uniques[finalOpeningStyle.title] = { title: finalOpeningStyle.title, code: finalOpeningStyle.openingStyleCode };
            });

            let converted = [{ title: 'All Opening Styles', code: 'ALL' }];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;

        }

        function presentZoneNames(zones){

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};
            zones.forEach(x => {
                uniques[x.zoneDescription] = { title: x.zoneDescription, code: x.zoneDescription };
            });

            let converted = [{ title: 'All Zone Names', code: 'ALL' }];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;

        }

        function presentNccClassifications(allZones) {

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};

            uniques["All Classifications"] = { title: "All Classifications", code: "ALL" };

            allZones.forEach(x => {

                if (x.nccClassification == null)
                    return;

                uniques[x.nccClassification?.description] = {
                    title: x.nccClassification.description,
                    code:  x.nccClassification.nccClassificationCode
                };
            });

            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;
        }

        function presentZoneActivities(allZones) {

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};

            uniques["All Zone Activities"] = { title: "All Zones Activities", code: "ALL" };

            allZones.forEach(x => {

                uniques[x.zoneActivity.description] = {
                    title: x.zoneActivity.description,
                    code:  x.zoneActivity.zoneActivityCode
                };
            });

            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;
        }

        function presentZoneTypes(allZones) {

            // Figure out which unique opening styles are present in the construction data.
            const uniques = {};

            uniques["All Zone Types"] = { title: "All Zones Types", code: "ALL" };

            allZones.forEach(x => {

                uniques[x.zoneType.description] = {
                    title: x.zoneType.description,
                    code:  x.zoneType.zoneTypeCode
                };
            });

            let converted = [];
            for(let prop in uniques)
                converted.push(uniques[prop]);

            return converted;
        }

        vm.applyStoreySelectLogic = function(override, storey) {

            // I think it's safest to just clear the other select values here..
            setTimeout(() => {

                vm.applyGroupSelectLogic(override, override.group);

            }, 30);

        }

        vm.applyGroupSelectLogic = function(override, group) {

            setTimeout(() => {

                // Refine selection to only show what is available.
                let allExtGlazingElements = vm.building.openings
                    .filter(x => x.category.constructionCategoryCode === "ExteriorGlazing")
                    .map(x => x.elements)
                    .reduce((a, b) => [...a, ...b]);

                let zonesWithExtGlazing = zoneservice.interiorZones(vm.building.zones)
                    .filter(zone => allExtGlazingElements.some(y => y.parentZoneId === zone.linkId));

                if (override.storey != null && override.storey !== "ALL") {
                    zonesWithExtGlazing = zonesWithExtGlazing.filter(x => x.storey === override.storey.floor);
                    allExtGlazingElements = allExtGlazingElements.filter(x => x.storey === override.storey.floor);
                }

                if (override.group === "All Zones")
                    override.selectableArray = zonesummaryservice.allZonesOptions;

                if (override.group === "Zone Name")
                    override.selectableArray = presentZoneNames(zonesWithExtGlazing);

                if (override.group === "Zone Activity")
                    override.selectableArray = presentZoneActivities(zonesWithExtGlazing);

                if (override.group === "Zone Type")
                    override.selectableArray = presentZoneTypes(zonesWithExtGlazing);

                if (override.group === "Conditioning")
                    override.selectableArray = CONDITIONED_GROUP_OPTIONS;

                if (override.group === "NCC Classification")
                    override.selectableArray = presentNccClassifications(zonesWithExtGlazing);

                if (override.group === "Facing Sector")
                    override.selectableArray = presentFacingSectors(allExtGlazingElements);

                if (override.group === "Opening Style") {
                    override.selectableArray = presentOpeningStyles(allExtGlazingElements);
                }

                if (group === "All Zones")
                    override.selection = ["All Zones"];

            },
            30);

        }

        vm.applySelectionLogic = function(override, selection) {
            setTimeout(() => {

                // SELECT or DE-SELECT all selections when the 'ALL' option is clicked.
                if (selection.code === "ALL" && override.selection.some(x => x.code === "ALL")) {
                    override.selection = angular.copy(override.selectableArray);
                } else if (selection.code === "ALL" && !override.selection.some(x => x.code === "ALL")) {
                    override.selection = [];
                } else {

                    // Just make sure to de-select the "ALL" checkbox if anything else is selected.
                    override.selection = override.selection.filter(x => x.code !== "ALL");

                }

                // Force refresh (Was taking multiple seconds to reflect change otherwise...?)
                const phase = $rootScope.$$phase;
                if (!phase) {
                    $rootScope.$apply();
                }

            }, 50);

        }

        /** Converts the given azimuth degree into it's compass direction string representation. */
        function facingSector(azimuth)  {

            const facing = common.determineSectorType(azimuth, vm.selectedSectorDetermination.sectors);
            return facing.toUpperCase();
        }

        function applyOverrideToRow(row, window, override) {

            if (override.uValue != null)
                row.uValue = override.uValue;

            if (override.shgc != null)
                row.shgc = override.shgc;

            if (override.pOperation != null) {
                if (override.pOperation === "equalTo")
                    row.p = override.p;
                else if (override.pOperation === "minimumIs" && row.p < override.p)
                    row.p = override.p;
                else if (override.pOperation === "maximumIs" && row.p > override.p)
                    row.p = override.p;
                else if (override.pOperation === "noShading") {
                    row.p = null;
                    override.p = null;
                    row.g = null;
                    override.g = null;
                    row.h = null;
                    override.h = null;
                    row.hMax = null;
                    override.hMax = null;
                }
            }

            // "G" replaces the value of 'shadeOffset' (AKA 'Vertical Offset') used originally
            if (override.g != null)
                row.h = override.g + window.height;

            if (override.hMax != null && row.h > override.hMax)
                row.h = override.hMax;

        }

        function applyOverridesIfNeeded(row, window, zone) {

            // Loop over all overrides and if this row matches any of the criteria, apply it.
            // Note that it's (theoretically) possible for a row to match multiple overrides.
            // So in that case we apply them in order (so last takes precedence).

            vm.manualOverrides.forEach(o => {

                let applyOverrides = false;

                // Not on matching storey so just continue.
                if (o.storey !== "ALL" && o.storey?.floor !== zone.storey)
                    return;

                // If these 2 conditions are met, ALL windows within the storey meet this criteria.
                if (o.group === "All Zones" && o.selection.includes("All Zones"))
                    applyOverrides = true;

                if (o.group === "Zone Name") {
                    if (o.selection.some(x => x.code === "ALL" || x.code === zone.zoneDescription))
                        applyOverrides = true;
                }

                if (o.group === "Zone Activity") {
                    const code = zone.zoneActivity.zoneActivityCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        applyOverrides = true;
                }

                if (o.group === "Zone Type") {
                    const code = zone.zoneType.zoneTypeCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        applyOverrides = true;
                }

                if (o.group === "Conditioning") {
                    if (o.selection.includes("All Conditioned Zones") && zone.conditioned === true)
                        applyOverrides = true;
                    else if (o.selection.includes("All Unconditioned Zones") && zone.conditioned === false)
                        applyOverrides = true;
                }

                if (o.group === "NCC Classification") {
                    const code = zone.nccClassification.nccClassificationCode;
                    if (o.selection.some(x => x.code === "ALL" || x.code === code))
                        applyOverrides = true;
                }

                if (o.group === "Facing Sector") {
                    if (o.selection.some(x => x.code === row.facingSector))
                        applyOverrides = true;
                }

                if (o.group === "Opening Style") {
                    const finalOpeningStyle = window.overrideOpeningStyle || window.openingStyle;
                    if (o.selection.some(x => x.code === "ALL" || x.code === finalOpeningStyle.openingStyleCode))
                        applyOverrides = true;
                }

                if (applyOverrides)
                    applyOverrideToRow(row, window, o);

            });

        }

        vm.GROUP_OPTIONS = [
            "All Zones",
            "Zone Name",
            "Zone Activity",
            "Zone Type",
            "Conditioning",
            "NCC Classification",
            "Facing Sector",
            "Opening Style",
        ];

        const ZONE_ACTIVITY_GROUP_OPTIONS = [
            { title: "All Zones",                       code: "ALL" },
            { title: "All Kitchen/Living/Dining Zones", code: "ZAKitchenLiving" },
            { title: "All Living Zones",                code: "ZALiving" },
            { title: "All Day Time Zones",              code: "ZADayTime" },
            { title: "All Bedroom Zones",               code: "ZABedroom" },
            { title: "All Night Time Zones",            code: "ZANightTime" },
            { title: "All Unconditioned Zones",         code: "ZAUnconditioned" },
            { title: "All Garage Zones",                code: "ZAGarage" },
            { title: "All Garage Conditioned Zones",    code: "ZAGarageConditioned" },
        ];

        const ZONE_TYPE_OPTIONS = [
            { title: "All Zones",                       code: "ALL" },
            { title: "All Habitable Rooms",             code: "ZTHabitableRoom" },
            { title: "All Non-Habitable Rooms",         code: "ZTNonHabitableRoom" },
            { title: "All Interconnecting Spaces",      code: "ZTInterconnecting" },
            { title: "All Class 10a Zones",             code: "ZTClass10A" },
        ];

        const CONDITIONED_GROUP_OPTIONS = [
            //"Conditioning",
            "All Conditioned Zones",
            "All Unconditioned Zones",
        ];

        const NCC_CLASSIFICATION_GROUP_OPTIONS = [
            { title: "All Zones",                       code: "ALL" },
            { title: "All Class 1a Zones",              code: "Class1A" },
            { title: "All Class 10a Zones",             code: "Class10A" },
        ];

        const FACING_SECTOR_GROUP_OPTIONS = [
            "N",
            "NE",
            "E",
            "SE",
            "S",
            "SW",
            "W",
            "NW",
        ];

        // All primary file types
        vm.softwareFileTypes = assessmentsoftwareservice.primarySoftwareFileTypes;

        initialize();

        class GlazingCalcConverter {

            /**
             * Determines the 'level type' of the zone (NOT storey) the current window is in.
             *
             * @param {any} window The window we wish to determine the 'level type' for.
             * @param {[]} floorConstructions PRE-FILTERED array of all possible floor construction ELEMENTS (NOT parents).
             * **/
            static determineLevelType(window, floorConstructions) {

                // Level type calculation
                //const windowZone = zones.filter(x => x.linkId === window.parentZoneId);

                // Select floor construction with largest area
                const floor = floorConstructions
                    .filter(x => x.parentZoneId === window.parentZoneId)
                    .sort((a, b) => a.grossArea - b.grossArea)
                    [0];

                if (floor == null)
                    return "Unknown";

                // 1. if the Parent Zone (where the Exterior Glazing is located) has a floor construction that is
                //    Exterior Floor (Connected to Ground), Level/Floor Type = Ground: Direct
                if (floor.category.constructionCategoryCode === "GroundFloor")
                    return "Ground: Direct";

                // 2. if the Parent Zone (where the Exterior Glazing is located) has a floor construction that is
                // Exterior Floor (Suspended) AND Subfloor Ventilation = Elevated AND the Parent Zone is located on the
                // lowest storey of the building, Level/Floor Type =  Ground: Suspended
                if (floor.category.constructionCategoryCode === "ExteriorFloorElevated" && floor.storey === 0)
                    return "Ground: Suspended";

                // 3. if the Parent Zone (where the Exterior Glazing is located) has a floor construction that is
                // Exterior Floor (Suspended) AND Subfloor Ventilation = Elevated AND the Parent Zone is located on a
                // storey the is NOT the lowest storey of the building, Level/Floor Type =  Upper
                if (floor.category.constructionCategoryCode === "ExteriorFloorElevated" && floor.storey !== 0)
                    return "Upper";

                // 4. if the Parent Zone (where the Exterior Glazing is located) has a floor construction that is
                // Exterior Floor (Suspended) AND Subfloor Ventilation = Enclosed OR Enclosed Disconnected OR Open OR
                // Very Open, Level/Floor Type = Ground: Suspended
                if (floor.category.constructionCategoryCode === "ExteriorFloor" &&
                    (floor.airCavity.airCavityCode === "SubfloorEnclosed" ||
                     floor.airCavity.airCavityCode === "SubfloorEnclosedDisconnected" ||
                     floor.airCavity.airCavityCode === "SubfloorOpen" ||
                     floor.airCavity.airCavityCode === "SubfloorVeryOpen"))
                    return "Ground: Suspended";

                // 5. if the Parent Zone (where the Exterior Glazing is located) has a floor construction that is
                //    Intermediate OR Intermediate (Neighbour Below), Level/Floor Type = Upper
                if (floor.category.constructionCategoryCode === "IntermediateFloor" ||
                     floor.category.constructionCategoryCode === "IntermediateFloorNeighbourBelow")
                    return "Upper";

                return "Unknown";

            }

            static determineAdjacentFloorCovering(window, floorConstructions, adjacentFloorCoveringList) {

                // Grab the largest floor construction in this windows parent zone, and then extract its last material
                // code.
                const floor = floorConstructions
                    .filter(x => x.parentZoneId === window.parentZoneId)
                    .sort((a, b) => a.grossArea - b.grossArea)
                    [0];

                if (floor.materialLayers == null) {
                    console.warn("floor.materialLayers was null. This indicates some data has not saved properly " +
                      "and the scratch file should be re-processed. For now, continuing with default values.");

                    common.logger.logWarning("Potential issue with determining adjacent floor coverings. Please re-process the associated scratch file.", null, null, true);

                    return adjacentFloorCoveringList.filter(x => x.isDefault === true)[0];
                }

                const materialCode = floor.materialLayers[floor.materialLayers.length - 1].codeAsInt;

                // Loop over all known adjacent floor coverings and check to see if any of the material codes associated
                // with them match up with the material code of our floor.
                for(let i = 0; i < adjacentFloorCoveringList.length; i++) {
                    const floorCovering = adjacentFloorCoveringList[i];

                    if (floorCovering.materialCodesArray.some(x => x === materialCode))
                        return floorCovering;
                }

                // None found, return whatever is set as the default.
                return adjacentFloorCoveringList.filter(x => x.isDefault === true)[0];

            }

            static determineBedroomOrUtility(window, windowZone, adjacentFloorCoveringCode) {

                // if Parent Zone has Zone Activity = Bedroom, Bedroom / Utility = Bedroom
                if (windowZone.zoneActivity.zoneActivityCode === "ZABedroom")
                    return "Bedroom";

                // if Parent Zone has Zone Activity = Night Time
                if (windowZone.zoneActivity.zoneActivityCode === "ZANightTime") {

                    // AND Zone Type = Non-Habitable Room
                    if (windowZone.zoneType.zoneTypeCode === "ZTNonHabitableRoom") {

                        // AND Floor Covering = Ceramic Tile, Bedroom / Utility = Utility
                        if (adjacentFloorCoveringCode === "CeramicTile")
                            return "Utility";
                        else
                            return "Bedroom"
                    } else
                        return "Bedroom";
                }

                // if Parent Zone has Zone Activity = Unconditioned, Bedroom / Utility = Utility
                // if Parent Zone has Zone Activity = Garage, Bedroom / Utility = Utility
                if (windowZone.zoneActivity.zoneActivityCode === "ZAUnconditioned" ||
                    windowZone.zoneActivity.zoneActivityCode === "ZAGarage")
                    return "Utility";

                // if Parent Zone has Zone Activity = Kitchen / Living / Dining, Bedroom / Utility = Other
                // if Parent Zone has Zone Activity = Living, Bedroom / Utility = Other
                // if Parent Zone has Zone Activity = Garage Conditioned, Bedroom / Utility = Other
                if (windowZone.zoneActivity.zoneActivityCode === "ZAKitchenLiving" ||
                   windowZone.zoneActivity.zoneActivityCode === "ZALiving" ||
                   windowZone.zoneActivity.zoneActivityCode === "ZAGarageConditioned")
                    return "Other";

                // if Parent Zone has Zone Activity = Day Time
                if (windowZone.zoneActivity.zoneActivityCode === "ZADayTime") {

                    // AND Zone Type = Non-habitable Room, Bedroom / Utility  = Utility
                    if (windowZone.zoneType.zoneTypeCode === "ZTNonHabitableRoom")
                        return "Utility"
                    else
                        // AND Zone Type = any other value (i.e. NOT Non-habitable room), Bedroom / Utility = Other
                        return "Other";
                }

                // Perhaps we should just be returning "Other" here...? I feel like this list is not exhaustive.
                return "Unknown";

            }

            static determineColour(solarAbsorptance) {

                if (solarAbsorptance <= 0.4)
                    return "Light";
                else if (solarAbsorptance > 0.4 && solarAbsorptance <= 0.6)
                    return "Medium"
                else
                    return "Dark";
            }
        }
    }

})();